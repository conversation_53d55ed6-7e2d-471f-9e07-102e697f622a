
cov-build --dir /home/<USER>/coverity/result --tmpdir /home/<USER>/coverity/tmp --encoding UTF-8 make

cov-analyze --dir /home/<USER>/ext/coverity/app/result --strip-path ~/work/bcd_508wgbi_mainboard/Code/ --coding-standard-config /home/<USER>/ext/Downloads/cov-analysis-linux64-2024.3.1/config/coding-standards/misrac2012/misrac2012-all.config --jobs max8

cov-commit-defects --host ************ --dataport 443 --on-new-cert trust --stream fridge508wgbi_app_misra2012 --dir /home/<USER>/ext/coverity/app/result  --user dajiadian --password dajiadian123